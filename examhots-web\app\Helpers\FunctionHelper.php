<?php

if (!function_exists('generateToken')) {
    function generateToken($length = 3)
    {
        $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        $charactersLength = strlen($characters);
        $token = '';

        for ($i = 0; $i < $length; $i++) {
            $token .= $characters[random_int(0, $charactersLength - 1)];
        }

        return $token;
    }
}

if (!function_exists('safeHtml')) {
    /**
     * Safely display HTML content by sanitizing and allowing only safe HTML tags
     */
    function safeHtml($content)
    {
        if (empty($content)) {
            return $content;
        }

        // Trim whitespace first
        $content = trim($content);

        // If content doesn't contain HTML tags, return as is (escaped for safety)
        if (strip_tags($content) === $content) {
            return e($content);
        }

        // Allow only basic formatting tags and sanitize the rest
        $allowedTags = '<p><br><strong><b><em><i><u><ol><ul><li><h1><h2><h3><h4><h5><h6>';

        // Strip unwanted tags but keep allowed ones
        $content = strip_tags($content, $allowedTags);

        // Remove any remaining script or style content
        $content = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', '', $content);
        $content = preg_replace('/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/mi', '', $content);

        // Remove any onclick, onload, etc. event handlers
        $content = preg_replace('/\s*on\w+\s*=\s*["\'][^"\']*["\']/i', '', $content);

        // Remove javascript: links
        $content = preg_replace('/javascript\s*:/i', '', $content);

        return trim($content);
    }
}
