# Bug Fix: HTML Tags in Questions and Answers

## Problem Description
In the Examhots web application, some questions and answers in the "Jadwal Ujian -> Lihat Soal" section were displaying HTML tags instead of properly formatted content. This was a security and display issue where:

1. Raw HTML tags were being stored in the database
2. HTML content was being rendered using `{!! !!}` without proper sanitization
3. This could lead to XSS vulnerabilities and poor user experience

## Root Cause
The issue was in the `cleanHtml()` function in both:
- `app/Http/Controllers/QuestionController.php`
- `app/Http/Controllers/Api/QuestionApiController.php`

The original function only trimmed whitespace but preserved all HTML tags, including potentially dangerous ones.

## Solution Implemented

### 1. Enhanced HTML Sanitization
Updated the `cleanHtml()` function to:
- Allow only safe HTML formatting tags: `<p><br><strong><b><em><i><u><ol><ul><li><h1><h2><h3><h4><h5><h6>`
- Remove dangerous tags like `<script>`, `<style>`, `<img>`, `<div>`, etc.
- Strip event handlers like `onclick`, `onload`, etc.
- Remove `javascript:` links
- Preserve basic text formatting while ensuring security

### 2. Added Helper Function
Created a `safeHtml()` helper function in `app/Helpers/FunctionHelper.php` that provides the same sanitization for use in Blade templates.

### 3. Updated Display Templates
Modified all Blade templates that display questions and answers to use the `safeHtml()` helper:

**Exam Schedule Components:**
- `resources/views/admin/exam/schedule/components/pilihan_ganda.blade.php`
- `resources/views/admin/exam/schedule/components/uraian_singkat.blade.php`
- `resources/views/admin/exam/schedule/components/esai.blade.php`

**Question Bank Components:**
- `resources/views/admin/exam/questionbank/components/pilihan_ganda.blade.php`
- `resources/views/admin/exam/questionbank/components/uraian_singkat.blade.php`
- `resources/views/admin/exam/questionbank/components/esai.blade.php`

### 4. Database Cleanup Command
Created `app/Console/Commands/CleanQuestionHtml.php` to clean existing data in the database.

## Changes Made

### Before:
```php
private function cleanHtml($content)
{
    if (empty($content)) {
        return $content;
    }
    // Only trim whitespace, preserve HTML tags
    return trim($content);
}
```

```blade
{!! $q->question !!}
{!! $answer->answer !!}
```

### After:
```php
private function cleanHtml($content)
{
    if (empty($content)) {
        return $content;
    }

    $content = trim($content);
    
    if (strip_tags($content) === $content) {
        return $content;
    }
    
    $allowedTags = '<p><br><strong><b><em><i><u><ol><ul><li><h1><h2><h3><h4><h5><h6>';
    $content = strip_tags($content, $allowedTags);
    
    // Remove dangerous content
    $content = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', '', $content);
    $content = preg_replace('/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/mi', '', $content);
    $content = preg_replace('/\s*on\w+\s*=\s*["\'][^"\']*["\']/i', '', $content);
    $content = preg_replace('/javascript\s*:/i', '', $content);
    
    return trim($content);
}
```

```blade
{!! safeHtml($q->question) !!}
{!! safeHtml($answer->answer) !!}
```

## Testing Results
- ✅ Normal text remains unchanged
- ✅ Dangerous tags like `<script>` are removed
- ✅ Safe formatting tags like `<p>`, `<strong>`, `<b>`, `<i>` are preserved
- ✅ Event handlers like `onclick` are stripped
- ✅ Malicious content is neutralized

## Security Benefits
1. **XSS Prevention**: Removes script tags and event handlers
2. **Content Sanitization**: Only allows safe HTML formatting
3. **Backward Compatibility**: Preserves existing safe formatting
4. **User Experience**: Maintains intended text formatting while ensuring security

## Files Modified
1. `app/Http/Controllers/QuestionController.php`
2. `app/Http/Controllers/Api/QuestionApiController.php`
3. `app/Helpers/FunctionHelper.php`
4. `resources/views/admin/exam/schedule/components/*.blade.php` (3 files)
5. `resources/views/admin/exam/questionbank/components/*.blade.php` (3 files)
6. `app/Console/Commands/CleanQuestionHtml.php` (new file)

## Usage
To clean existing data in the database:
```bash
php artisan questions:clean-html
```

This fix ensures that all questions and answers are properly sanitized both when stored and when displayed, preventing HTML injection while preserving legitimate formatting.
