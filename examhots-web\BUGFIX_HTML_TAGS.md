# Bug Fix: HTML Tags in Student Detail View

## Problem Description
In the Examhots web application, questions and answers in the "Jadwal Ujian -> Lihat Soal" section, specifically in the `student-detail.blade.php` file, were displaying HTML tags as raw text instead of properly formatted content. This was happening because:

1. Questions and answers containing HTML formatting were being displayed using `{{ }}` (escaped output)
2. This caused HTML tags like `<p>`, `<strong>`, `<em>` to be displayed as literal text instead of being rendered as HTML
3. Users would see things like `&lt;p&gt;Question text&lt;/p&gt;` instead of properly formatted content

## Root Cause
The issue was in the `student-detail.blade.php` file where questions and answers were being displayed using <PERSON><PERSON>'s escaped output syntax `{{ }}` instead of unescaped output `{!! !!}`. However, simply changing to `{!! !!}` would create security vulnerabilities, so we needed to implement safe HTML rendering.

## Solution Implemented

### 1. Enhanced HTML Sanitization
Updated the `cleanHtml()` function to:
- Allow only safe HTML formatting tags: `<p><br><strong><b><em><i><u><ol><ul><li><h1><h2><h3><h4><h5><h6>`
- Remove dangerous tags like `<script>`, `<style>`, `<img>`, `<div>`, etc.
- Strip event handlers like `onclick`, `onload`, etc.
- Remove `javascript:` links
- Preserve basic text formatting while ensuring security

### 2. Added Helper Function
Created a `safeHtml()` helper function in `app/Helpers/FunctionHelper.php` that provides the same sanitization for use in Blade templates.

### 3. Updated Student Detail Template
Modified the main file that was causing the issue:

**Student Detail View:**
- `resources/views/admin/exam/schedule/student-detail.blade.php`

**Additional Components (for consistency):**
- `resources/views/admin/exam/schedule/components/pilihan_ganda.blade.php`
- `resources/views/admin/exam/schedule/components/uraian_singkat.blade.php`
- `resources/views/admin/exam/schedule/components/esai.blade.php`
- `resources/views/admin/exam/questionbank/components/pilihan_ganda.blade.php`
- `resources/views/admin/exam/questionbank/components/uraian_singkat.blade.php`
- `resources/views/admin/exam/questionbank/components/esai.blade.php`

### 4. Database Cleanup Command
Created `app/Console/Commands/CleanQuestionHtml.php` to clean existing data in the database.

## Changes Made

### Before (in student-detail.blade.php):
```blade
<!-- Question display -->
<h3 class="font-medium text-gray-900 mb-2">
    {{ $loop->iteration }}. {{ $question->question }}
</h3>

<!-- Answer display for multiple choice -->
<span class="text-gray-900">{{ $answer->answer }}</span>

<!-- Answer display for short answer -->
<p class="text-blue-900">{{ $correctAnswer->answer ?? 'Tidak ada jawaban yang benar' }}</p>

<!-- Student answer display -->
<p class="text-gray-900 flex-1">{{ $studentAnswerText ?: 'Tidak dijawab' }}</p>
```

### After (in student-detail.blade.php):
```blade
<!-- Question display with proper CSS class -->
<h3 class="font-medium text-gray-900 mb-2">
    <span class="mr-2">{{ $loop->iteration }}.</span>
    <div class="question-content inline">{!! safeHtml($question->question) !!}</div>
</h3>

<!-- Answer display for multiple choice with CSS class -->
<div class="answer-content text-gray-900">{!! safeHtml($answer->answer) !!}</div>

<!-- Answer display for short answer with CSS class -->
<div class="answer-content text-blue-900">{!! safeHtml($correctAnswer->answer ?? 'Tidak ada jawaban yang benar') !!}</div>

<!-- Student answer display with CSS class -->
<div class="answer-content text-gray-900 flex-1">{!! safeHtml($studentAnswerText ?: 'Tidak dijawab') !!}</div>
```

## Testing Results
- ✅ Normal text remains unchanged
- ✅ Dangerous tags like `<script>` are removed
- ✅ Safe formatting tags like `<p>`, `<strong>`, `<b>`, `<i>` are preserved
- ✅ Event handlers like `onclick` are stripped
- ✅ Malicious content is neutralized
- ✅ Bullet points in lists (`<ul>`, `<ol>`) now display correctly
- ✅ CSS classes `question-content` and `answer-content` ensure proper formatting

## Security Benefits
1. **XSS Prevention**: Removes script tags and event handlers
2. **Content Sanitization**: Only allows safe HTML formatting
3. **Backward Compatibility**: Preserves existing safe formatting
4. **User Experience**: Maintains intended text formatting while ensuring security

## Files Modified
1. `resources/views/admin/exam/schedule/student-detail.blade.php` (main fix)
2. `app/Helpers/FunctionHelper.php` (added safeHtml helper)
3. `app/Http/Controllers/QuestionController.php` (enhanced cleanHtml function)
4. `app/Http/Controllers/Api/QuestionApiController.php` (enhanced cleanHtml function)
5. `resources/views/admin/exam/schedule/components/*.blade.php` (3 files for consistency)
6. `resources/views/admin/exam/questionbank/components/*.blade.php` (3 files for consistency)
7. `app/Console/Commands/CleanQuestionHtml.php` (new file for database cleanup)

## Usage
To clean existing data in the database:
```bash
php artisan questions:clean-html
```

This fix ensures that all questions and answers are properly sanitized both when stored and when displayed, preventing HTML injection while preserving legitimate formatting.
