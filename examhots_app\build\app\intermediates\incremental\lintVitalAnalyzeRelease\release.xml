<variant
    name="release"
    package="com.example.examhots_app"
    minSdkVersion="21"
    targetSdkVersion="35"
    shrinking="true"
    mergedManifest="C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\app\intermediates\merged_manifest\release\processReleaseMainManifest\AndroidManifest.xml"
    proguardFiles="C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\app\intermediates\default_proguard_files\global\proguard-android-optimize.txt-8.7.0;C:\Users\<USER>\AppData\Local\flutter\packages\flutter_tools\gradle\flutter_proguard_rules.pro;proguard-rules.pro"
    partialResultsDir="C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\app\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
    desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eb8d2aafc14870cea60e6e193ee2af5c\transformed\D8BackportedDesugaredMethods.txt">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\release\java;src\main\kotlin;src\release\kotlin"
        resDirectories="src\main\res;src\release\res"
        assetsDirectories="src\main\assets;src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <manifestPlaceholders>
    <placeholder
        name="applicationName"
        value="android.app.Application" />
  </manifestPlaceholders>
  <artifact
      classOutputs="C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\app\intermediates\javac\release\compileReleaseJavaWithJavac\classes;C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\app\tmp\kotlin-classes\release;C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\app\kotlinToolingMetadata;C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\app\intermediates\compile_and_runtime_not_namespaced_r_class_jar\release\processReleaseResources\R.jar"
      type="MAIN"
      applicationId="com.example.examhots_app"
      generatedSourceFolders="C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\app\generated\ap_generated_sources\release\out"
      generatedResourceFolders="C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\app\generated\res\resValues\release"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eb8d2aafc14870cea60e6e193ee2af5c\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
