<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Question;
use App\Models\Answer;

class CleanQuestionHtml extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'questions:clean-html';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean HTML content in questions and answers to remove unwanted tags';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting to clean HTML content in questions and answers...');

        // Clean questions
        $questions = Question::all();
        $questionCount = 0;

        foreach ($questions as $question) {
            $originalQuestion = $question->question;
            $cleanedQuestion = $this->cleanHtml($originalQuestion);
            
            if ($originalQuestion !== $cleanedQuestion) {
                $question->question = $cleanedQuestion;
                $question->save();
                $questionCount++;
                $this->line("Cleaned question ID: {$question->id}");
            }
        }

        // Clean answers
        $answers = Answer::all();
        $answerCount = 0;

        foreach ($answers as $answer) {
            $originalAnswer = $answer->answer;
            $cleanedAnswer = $this->cleanHtml($originalAnswer);
            
            if ($originalAnswer !== $cleanedAnswer) {
                $answer->answer = $cleanedAnswer;
                $answer->save();
                $answerCount++;
                $this->line("Cleaned answer ID: {$answer->id}");
            }
        }

        $this->info("Cleaning completed!");
        $this->info("Questions cleaned: {$questionCount}");
        $this->info("Answers cleaned: {$answerCount}");
    }

    /**
     * Clean HTML content by sanitizing and allowing only safe HTML tags
     */
    private function cleanHtml($content)
    {
        if (empty($content)) {
            return $content;
        }

        // Trim whitespace first
        $content = trim($content);
        
        // If content doesn't contain HTML tags, return as is
        if (strip_tags($content) === $content) {
            return $content;
        }
        
        // Allow only basic formatting tags and sanitize the rest
        $allowedTags = '<p><br><strong><b><em><i><u><ol><ul><li><h1><h2><h3><h4><h5><h6>';
        
        // Strip unwanted tags but keep allowed ones
        $content = strip_tags($content, $allowedTags);
        
        // Remove any remaining script or style content
        $content = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', '', $content);
        $content = preg_replace('/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/mi', '', $content);
        
        // Remove any onclick, onload, etc. event handlers
        $content = preg_replace('/\s*on\w+\s*=\s*["\'][^"\']*["\']/i', '', $content);
        
        // Remove javascript: links
        $content = preg_replace('/javascript\s*:/i', '', $content);
        
        return trim($content);
    }
}
